#!/usr/bin/env python3
"""
Test script to verify that the showToolOutput flag works correctly.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, '/home/<USER>/django-projects/agbase_admin')

from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib

async def test_tool_output_flag():
    """Test that the showToolOutput flag is properly loaded and respected."""
    
    print("Testing showToolOutput flag functionality...")
    print("=" * 50)
    
    # Create client
    def debug_callback(level, message, data=None):
        print(f"[{level.upper()}] {message}")
        if data and level == "debug":
            print(f"  Data: {data}")
    
    client = MCPClientLib(debug_callback=debug_callback)
    
    try:
        # Connect to server
        print("\n1. Connecting to MCP HTTP server...")
        success = await client.connect_to_server("http://localhost:9000/mcp")
        
        if not success:
            print("❌ Failed to connect to server. Make sure the MCP HTTP server is running.")
            return
        
        print("✅ Connected successfully!")
        
        # Check global settings
        print(f"\n2. Global settings loaded: {client.global_settings}")
        show_output = client.should_show_tool_output()
        print(f"3. Should show tool output: {show_output}")
        
        # Test calling a tool
        print("\n4. Testing tool call...")
        result = await client.call_tool("echostring", {"phrase": "Hello, World!"})
        
        if result.success:
            print(f"✅ Tool call successful: {result.content}")
        else:
            print(f"❌ Tool call failed: {result.error}")
        
        # Test the process_query method (this is what chat_term uses)
        print("\n5. Testing process_query method...")
        query_result = await client.process_query(
            query="Please echo the phrase 'Testing tool output flag'",
            model="claude-3-5-sonnet-20240620",
            max_tokens=100
        )
        
        print(f"Query result: {query_result['final_text']}")
        print(f"Tool results count: {len(query_result['tool_results'])}")
        
        if query_result['tool_results']:
            print("Tool results were returned (this is expected)")
            print("The showToolOutput flag should control whether these are PRINTED in chat_term")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        await client.cleanup()
        print("\n✅ Test completed!")

if __name__ == "__main__":
    asyncio.run(test_tool_output_flag())
