#!/usr/bin/env python3
"""
Terminal-based chat interface using the chatobj implementation.

This script provides a REPL-style interface for interacting with the chat system,
allowing users to create, manage, and use conversations directly from the terminal.

Usage:
    python chat_term.py [--storage-dir DIR] [--user-id ID] [--llm PROVIDER] [--model MODEL]

Supported LLM providers:
    - mock: Uses a simple mock LLM for testing
    - openai: Uses OpenAI's API
    - anthropic: Uses Anthropic's API
    - mcp: Uses MCP SSE client to connect to a tool server
    - mcp-http: Uses MCP HTTP client to connect to a tool server
"""

import argparse
import json
import logging
import os
import readline  # Enables arrow key navigation and command history
import shlex
import sys
import asyncio
import re
from datetime import datetime
from typing import Dict, Any, List, Optional

from gaia.gaia_ceto.ceto_v002.chatobj import ChatManager, MockLLM, OpenAILLM, AnthropicLLM, Conversation

# Import MCP client libraries if available
MCP_SSE_AVAILABLE = False
MCP_HTTP_AVAILABLE = False
FASTMCP_AVAILABLE = False

# Try to import SSE client
try:
    from gaia.gaia_ceto.proto_mcp.mcp_sse_clientlib import MCPClientLib as MCPSSEClientLib
    MCP_SSE_AVAILABLE = True
except ImportError:
    print("Warning: MCP SSE client library not available. MCP SSE provider will not work.")
    print("To enable MCP SSE support, ensure the mcp_sse_clientlib.py file is in the correct location.")

# Try to import HTTP client
try:
    from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib as MCPHTTPClientLib
    MCP_HTTP_AVAILABLE = True
except ImportError:
    print("Warning: MCP HTTP client library not available. MCP HTTP provider will not work.")
    print("To enable MCP HTTP support, ensure the mcp_http_clientlib.py file is in the correct location.")

# Try to import FastMCP for progress support
try:
    from fastmcp import Client
    from fastmcp.client.logging import LogMessage
    FASTMCP_AVAILABLE = True
except ImportError:
    print("Warning: FastMCP not available. Progress display for direct tool calls will not work.")
    # Create a dummy LogMessage class for type hints when FastMCP is not available
    class LogMessage:
        def __init__(self):
            self.level = ""
            self.logger = ""
            self.data = ""
    FASTMCP_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("chat_term.log"),
        logging.StreamHandler(stream=sys.stderr)
    ]
)
logger = logging.getLogger("chat_term")


class MCPSSELLM:
    """MCP SSE client implementation of the LLM interface."""

    def __init__(self, server_url: str = "http://0.0.0.0:9000/sse", model_name: str = "claude-3-5-sonnet-20240620"):
        """Initialize the MCP SSE LLM.

        Args:
            server_url: The URL of the MCP SSE server.
            model_name: The name of the Claude model to use.
        """
        if not MCP_SSE_AVAILABLE:
            raise ImportError("MCP SSE client library is not available. Cannot use MCP SSE LLM.")

        self.server_url = server_url
        self.model_name = model_name
        self.client = None
        self.messages = []
        self.loop = asyncio.new_event_loop()

        # Initialize the MCP client
        self.client = self._init_client()

    def _init_client(self):
        """Initialize the MCP SSE client."""
        client = MCPSSEClientLib(debug_callback=self._debug_callback)

        # Connect to the server
        print(f"Connecting to MCP server via SSE at: {self.server_url}")
        success = self.loop.run_until_complete(client.connect_to_server(self.server_url))

        if not success:
            raise ConnectionError(f"Failed to connect to MCP server at {self.server_url}")

        # Print available tools
        tool_names = [tool['name'] for tool in client.available_tools]
        print(f"Connected to server with tools: {', '.join(tool_names)}")

        return client

    def should_show_tool_output(self) -> bool:
        """Check if tool output should be shown based on server global settings."""
        if self.client and hasattr(self.client, 'should_show_tool_output'):
            return self.client.should_show_tool_output()
        return True  # Default to showing output for backward compatibility

    def _debug_callback(self, level: str, message: str, data: Any = None):
        """Callback for debug messages from the client library."""
        if level == "error":
            logger.error(message)
        elif level == "warning":
            logger.warning(message)
        elif level == "info":
            logger.info(message)
        elif level == "debug":
            logger.debug(message)

    def generate_response(self, prompt: str, context: List[Dict[str, Any]], **kwargs) -> str:
        """Generate a response using the MCP SSE client.

        Args:
            prompt: The user's input text.
            context: The conversation history.
            **kwargs: Additional parameters to pass to the MCP client.

        Returns:
            The generated response.
        """
        try:
            # Convert context to the format expected by the MCP client
            # Anthropic API requires system messages to be handled differently
            messages = []

            # Extract only user and assistant messages for the messages array
            for msg in context:
                if msg["role"] in ["user", "assistant"]:
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })

            # Process the query
            result = self.loop.run_until_complete(self.client.process_query(
                query=prompt,
                model=self.model_name,
                max_tokens=kwargs.get("max_tokens", 1024),
                tool_timeout=kwargs.get("tool_timeout", 3600),  # for long-running tools
                conversation_history=messages
            ))

            # Update conversation history
            self.messages = result["messages"]

            # Print any errors
            if result["error"]:
                logger.error(f"Error from MCP SSE client: {result['error']}")
                return f"Error: {result['error']}"

            # Print tool results if any and if enabled in global settings
            if result["tool_results"] and self.should_show_tool_output():
                tool_calls_info = f"\nClaude requested {len(result['tool_results'])} tool call(s).\n"
                for i, tool_result in enumerate(result["tool_results"]):
                    tool_calls_info += f"\nTool Call {i+1}: '{tool_result.tool_name}'\n"
                    if tool_result.success:
                        tool_calls_info += f"Result: {tool_result.content}\n"
                    else:
                        tool_calls_info += f"Error: {tool_result.error}\n"

                print(tool_calls_info)

            # Return Claude's response
            return result["final_text"]

        except Exception as e:
            logger.error(f"Error generating response from MCP SSE: {e}")
            return f"Error generating response: {str(e)}"

    def __del__(self):
        """Clean up resources when the object is destroyed."""
        if self.client:
            try:
                self.loop.run_until_complete(self.client.cleanup())
            except Exception as e:
                logger.error(f"Error cleaning up MCP SSE client: {e}")

        # Close the event loop
        try:
            self.loop.close()
        except Exception as e:
            logger.error(f"Error closing event loop: {e}")


class MCPHTTPLLM:
    """MCP HTTP client implementation of the LLM interface."""

    def __init__(self, server_url: str = "http://0.0.0.0:9000/mcp", model_name: str = "claude-3-5-sonnet-20240620"):
        """Initialize the MCP HTTP LLM.

        Args:
            server_url: The URL of the MCP HTTP server.
            model_name: The name of the Claude model to use.
        """
        if not MCP_HTTP_AVAILABLE:
            raise ImportError("MCP HTTP client library is not available. Cannot use MCP HTTP LLM.")

        self.server_url = server_url
        self.model_name = model_name
        self.client = None
        self.messages = []
        self.loop = asyncio.new_event_loop()

        # Initialize the MCP client
        self.client = self._init_client()

    def _init_client(self):
        """Initialize the MCP HTTP client."""
        client = MCPHTTPClientLib(debug_callback=self._debug_callback)

        # Connect to the server
        print(f"Connecting to MCP server via HTTP at: {self.server_url}")
        success = self.loop.run_until_complete(client.connect_to_server(self.server_url))

        if not success:
            raise ConnectionError(f"Failed to connect to MCP server at {self.server_url}")

        # Print available tools
        tool_names = [tool['name'] for tool in client.available_tools]
        print(f"Connected to server with tools: {', '.join(tool_names)}")

        return client

    def should_show_tool_output(self) -> bool:
        """Check if tool output should be shown based on server global settings."""
        if self.client and hasattr(self.client, 'should_show_tool_output'):
            return self.client.should_show_tool_output()
        return True  # Default to showing output for backward compatibility

    def _debug_callback(self, level: str, message: str, data: Any = None):
        """Callback for debug messages from the client library."""
        if level == "error":
            logger.error(message)
        elif level == "warning":
            logger.warning(message)
        elif level == "info":
            logger.info(message)
        elif level == "debug":
            logger.debug(message)

    def generate_response(self, prompt: str, context: List[Dict[str, Any]], **kwargs) -> str:
        """Generate a response using the MCP HTTP client.

        Args:
            prompt: The user's input text.
            context: The conversation history.
            **kwargs: Additional parameters to pass to the MCP client.

        Returns:
            The generated response.
        """
        try:
            # Convert context to the format expected by the MCP client
            # Anthropic API requires system messages to be handled differently
            messages = []

            # Extract only user and assistant messages for the messages array
            for msg in context:
                if msg["role"] in ["user", "assistant"]:
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })

            # Process the query
            result = self.loop.run_until_complete(self.client.process_query(
                query=prompt,
                model=self.model_name,
                max_tokens=kwargs.get("max_tokens", 1024),
                tool_timeout=kwargs.get("tool_timeout", 3600),  # for long-running tools
                conversation_history=messages
            ))

            # Update conversation history
            self.messages = result["messages"]

            # Print any errors
            if result["error"]:
                logger.error(f"Error from MCP HTTP client: {result['error']}")
                return f"Error: {result['error']}"

            # Print tool results if any and if enabled in global settings
            if result["tool_results"] and self.should_show_tool_output():
                tool_calls_info = f"\nClaude requested {len(result['tool_results'])} tool call(s).\n"
                for i, tool_result in enumerate(result["tool_results"]):
                    tool_calls_info += f"\nTool Call {i+1}: '{tool_result.tool_name}'\n"
                    if tool_result.success:
                        tool_calls_info += f"Result: {tool_result.content}\n"
                    else:
                        tool_calls_info += f"Error: {tool_result.error}\n"

                print(tool_calls_info)

            # Return Claude's response
            return result["final_text"]

        except Exception as e:
            logger.error(f"Error generating response from MCP HTTP: {e}")
            return f"Error generating response: {str(e)}"

    def __del__(self):
        """Clean up resources when the object is destroyed."""
        if self.client:
            try:
                self.loop.run_until_complete(self.client.cleanup())
            except Exception as e:
                logger.error(f"Error cleaning up MCP HTTP client: {e}")

        # Close the event loop
        try:
            self.loop.close()
        except Exception as e:
            logger.error(f"Error closing event loop: {e}")


class ChatTerminal:
    """Terminal interface for the chat system."""

    def __init__(self, storage_dir: str = "./conversations", user_id: Optional[str] = None,
                 llm_provider: str = "mock", model_name: Optional[str] = None):
        """Initialize the chat terminal.

        Args:
            storage_dir: Directory to store conversations.
            user_id: Optional user ID to associate with new conversations.
            llm_provider: LLM provider to use ("mock", "openai", or "anthropic").
            model_name: Specific model name to use with the selected provider.
        """
        self.storage_dir = os.path.abspath(storage_dir)
        self.user_id = user_id or os.getenv("USER", "anonymous")
        self.llm_provider = llm_provider.lower()
        self.model_name = model_name

        # Initialize the appropriate LLM based on the provider
        llm = self._initialize_llm(self.llm_provider, self.model_name)

        # Create the chat manager with the selected LLM
        self.chat_manager = ChatManager(storage_dir=storage_dir, llm=llm)
        self.running = False
        self.command_history = []

        # Command handlers
        self.commands = {
            "help": self.cmd_help,
            "new": self.cmd_new,
            "list": self.cmd_list,
            "load": self.cmd_load,
            "load_context": self.cmd_load_context,
            "save": self.cmd_save,
            "delete": self.cmd_delete,
            "info": self.cmd_info,
            "stats": self.cmd_stats,
            "clear": self.cmd_clear,
            "exit": self.cmd_exit,
            "quit": self.cmd_exit,
        }

        # Command descriptions for help
        self.command_help = {
            "help": "Show this help message",
            "new [title]": "Create a new conversation with optional title",
            "list [year] [month]": "List conversations, optionally filtered by year/month",
            "load <id|path>": "Load a conversation by ID or path",
            "load_context <filename>": "Load contextual data from a JSON file into the current conversation",
            "save": "Save the current conversation",
            "delete <id|path>": "Delete a conversation by ID or path",
            "info": "Show information about the current conversation",
            "stats": "Show statistics about stored conversations",
            "clear": "Clear the terminal screen",
            "exit, quit": "Exit the chat terminal",
        }

        # Ensure storage directory exists
        os.makedirs(storage_dir, exist_ok=True)

        # Set up context directory path
        self.context_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "proto_mcp", "example_contexts")

        # Progress tracking for FastMCP
        self.progress_count = 0
        self.info_count = 0

    async def _progress_handler(self, progress: float, total: Optional[float], message: Optional[str]) -> None:
        """Handle progress updates from FastMCP client."""
        self.progress_count += 1

        if total:
            pct = progress / total * 100

            # Create visual progress bar
            bar_length = 28
            filled = int(bar_length * pct / 100)
            bar = "█" * filled + "░" * (bar_length - filled)

            # Format message
            msg_text = f" – {message}" if message else ""

            print(f"📊 [{bar}] {pct:5.1f}% {int(progress)}/{int(total)}{msg_text}", flush=True)
        else:
            print(f"📊 Step {progress} – {message or ''}", flush=True)

    async def _log_handler(self, message: LogMessage) -> None:
        """Handle log messages from FastMCP client."""
        level = message.level.upper()

        # Count info messages but don't display them for cleaner output
        if level == "INFO":
            self.info_count += 1

        # Only display non-info messages (errors, warnings, etc.)
        if level != "INFO":
            logger = message.logger or "server"
            payload = message.data
            emoji = {"ERROR": "❌", "WARNING": "⚠️", "DEBUG": "🔍"}.get(level, "ℹ️")
            print(f"{emoji} [{level}] {logger}: {payload}", flush=True)

    def _is_direct_tool_call(self, message: str) -> tuple[bool, Optional[str]]:
        """Check if the message is a direct tool call and extract the tool name.

        Args:
            message: The user's message.

        Returns:
            A tuple of (is_direct_call, tool_name).
        """
        # Check for direct tool call patterns like "long_task" or "tool_name(args)"
        # This is a simple heuristic - you might want to make it more sophisticated
        message = message.strip()

        # Pattern 1: Just the tool name
        if message in ["long_task", "echostring_longrunning", "echostring_quick_stage"]:
            return True, message

        # Pattern 2: Tool name with parentheses
        if re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*\(\)$', message):
            return True, message.replace('()', '')

        # Pattern 3: Tool name with arguments
        match = re.match(r'^([a-zA-Z_][a-zA-Z0-9_]*)\(.*\)$', message)
        if match:
            return True, match.group(1)

        return False, None

    async def _handle_direct_tool_call_with_progress(self, tool_name: str) -> Optional[str]:
        """Handle a direct tool call using FastMCP with progress display.

        Args:
            tool_name: The name of the tool to call.

        Returns:
            The result of the tool call, or None if it failed.
        """
        if not FASTMCP_AVAILABLE:
            print("❌ FastMCP not available. Cannot show progress for direct tool calls.")
            return None

        # Determine the server URL based on the current LLM provider
        if isinstance(self.chat_manager.llm, MCPSSELLM):
            url = self.chat_manager.llm.server_url
        elif isinstance(self.chat_manager.llm, MCPHTTPLLM):
            url = self.chat_manager.llm.server_url
        else:
            # Default to HTTP server
            url = "http://localhost:9000/mcp"

        # Reset counters
        self.progress_count = 0
        self.info_count = 0

        print(f"🚀 Calling {tool_name} with progress display...")

        try:
            client = Client(
                url,
                progress_handler=self._progress_handler,
                log_handler=self._log_handler,
            )

            async with client:
                print("✅ Connected (FastMCP client with progress)")

                # Call the tool - progress and info will be handled by callbacks
                result = await client.call_tool(tool_name)

            # Final summary
            print()
            print("🎉 Tool finished successfully")
            print(f"📊 Progress msgs: {self.progress_count}   ℹ️ Info msgs: {self.info_count}")

            # Return the result content if available
            if hasattr(result, 'content') and result.content:
                return str(result.content[0].text if hasattr(result.content[0], 'text') else result.content[0])
            else:
                return "Tool completed successfully"

        except Exception as e:
            print(f"❌ Error calling {tool_name}: {e}")
            return None

    def _initialize_llm(self, provider: str, model_name: Optional[str] = None):
        """Initialize the appropriate LLM based on the provider.

        Args:
            provider: The LLM provider ("mock", "openai", "anthropic", "mcp", or "mcp-http").
            model_name: Optional specific model name to use.

        Returns:
            An instance of the appropriate LLM class.

        Raises:
            ValueError: If the provider is not supported.
        """
        kwargs = {}
        if model_name:
            kwargs["model_name"] = model_name

        if provider == "mock":
            return MockLLM()
        elif provider == "openai":
            try:
                return OpenAILLM(**kwargs)
            except Exception as e:
                logger.error(f"Failed to initialize OpenAI LLM: {e}")
                logger.warning("Falling back to MockLLM")
                print(f"Error initializing OpenAI LLM: {e}")
                print("Falling back to MockLLM")
                return MockLLM()
        elif provider == "anthropic":
            try:
                return AnthropicLLM(**kwargs)
            except Exception as e:
                logger.error(f"Failed to initialize Anthropic LLM: {e}")
                logger.warning("Falling back to MockLLM")
                print(f"Error initializing Anthropic LLM: {e}")
                print("Falling back to MockLLM")
                return MockLLM()
        elif provider == "mcp":
            if not MCP_SSE_AVAILABLE:
                logger.error("MCP SSE client library is not available. Cannot use MCP SSE LLM.")
                logger.warning("Falling back to MockLLM")
                print("MCP SSE client library is not available. Cannot use MCP SSE LLM.")
                print("Falling back to MockLLM")
                return MockLLM()

            try:
                # Default server URL if not specified in model_name
                server_url = "http://0.0.0.0:9000/sse"

                # If model_name contains a URL, use it as the server URL
                if model_name and "://" in model_name:
                    server_url = model_name
                    # Reset model_name to None to use the default model
                    kwargs = {}

                return MCPSSELLM(server_url=server_url, **kwargs)
            except Exception as e:
                logger.error(f"Failed to initialize MCP SSE LLM: {e}")
                logger.warning("Falling back to MockLLM")
                print(f"Error initializing MCP SSE LLM: {e}")
                print("Falling back to MockLLM")
                return MockLLM()
        elif provider == "mcp-http":
            if not MCP_HTTP_AVAILABLE:
                logger.error("MCP HTTP client library is not available. Cannot use MCP HTTP LLM.")
                logger.warning("Falling back to MockLLM")
                print("MCP HTTP client library is not available. Cannot use MCP HTTP LLM.")
                print("Falling back to MockLLM")
                return MockLLM()

            try:
                # Default server URL if not specified in model_name
                server_url = "http://0.0.0.0:9000/mcp"

                # If model_name contains a URL, use it as the server URL
                if model_name and "://" in model_name:
                    server_url = model_name
                    # Reset model_name to None to use the default model
                    kwargs = {}

                return MCPHTTPLLM(server_url=server_url, **kwargs)
            except Exception as e:
                logger.error(f"Failed to initialize MCP HTTP LLM: {e}")
                logger.warning("Falling back to MockLLM")
                print(f"Error initializing MCP HTTP LLM: {e}")
                print("Falling back to MockLLM")
                return MockLLM()
        else:
            logger.error(f"Unsupported LLM provider: {provider}")
            logger.warning("Falling back to MockLLM")
            print(f"Unsupported LLM provider: {provider}")
            print("Falling back to MockLLM")
            return MockLLM()

    def start(self):
        """Start the chat terminal interface."""
        self.running = True
        self.print_welcome()

        while self.running:
            try:
                # Get the prompt based on active conversation
                prompt = self.get_prompt()

                # Get user input
                user_input = input(prompt).strip()

                # Skip empty input
                if not user_input:
                    continue

                # Add to command history
                self.command_history.append(user_input)

                # Process the input
                self.process_input(user_input)

            except KeyboardInterrupt:
                print("\nUse 'exit' or 'quit' to exit the chat terminal.")
            except EOFError:
                print("\nExiting...")
                self.running = False
            except Exception as e:
                logger.error(f"Error: {e}")
                print(f"Error: {e}")

    def get_prompt(self) -> str:
        """Get the appropriate prompt based on the active conversation.

        Returns:
            A string prompt for input.
        """
        if self.chat_manager.active_conversation:
            conv = self.chat_manager.active_conversation
            return f"\n[{conv.title}] > "
        else:
            return "\n[No active conversation] > "

    def process_input(self, user_input: str):
        """Process user input.

        Args:
            user_input: The user's input string.
        """
        # Check if it's a command (starts with /)
        if user_input.startswith("/"):
            self.handle_command(user_input[1:])
        else:
            self.handle_message(user_input)

    def handle_command(self, command_str: str):
        """Handle a command input.

        Args:
            command_str: The command string (without the leading /).
        """
        try:
            # Parse the command string
            parts = shlex.split(command_str)
            cmd = parts[0].lower() if parts else ""
            args = parts[1:] if len(parts) > 1 else []

            # Execute the command if it exists
            if cmd in self.commands:
                self.commands[cmd](*args)
            else:
                print(f"Unknown command: {cmd}")
                print("Type '/help' for available commands.")
        except Exception as e:
            logger.error(f"Error handling command '{command_str}': {e}")
            print(f"Error: {e}")

    def handle_message(self, message: str):
        """Handle a chat message input.

        Args:
            message: The user's message.
        """
        if not self.chat_manager.active_conversation:
            print("No active conversation. Create a new one with '/new' or load one with '/load'.")
            return

        try:
            # Check if this is a direct tool call and we're using an MCP provider
            is_direct_call, tool_name = self._is_direct_tool_call(message)

            if (is_direct_call and tool_name and
                isinstance(self.chat_manager.llm, (MCPSSELLM, MCPHTTPLLM)) and
                FASTMCP_AVAILABLE):

                print(f"🔍 Detected direct tool call: {tool_name}")
                print("📊 Using FastMCP for progress display...")

                # Handle with progress display
                result = asyncio.run(self._handle_direct_tool_call_with_progress(tool_name))

                if result:
                    # Add the user message and assistant response to conversation
                    self.chat_manager.add_message("user", message)
                    self.chat_manager.add_message("assistant", result)

                    # Display the response
                    print(f"\nAssistant: {result}")
                else:
                    print("❌ Direct tool call failed. Falling back to regular processing...")
                    # Fall back to regular processing
                    response = self.chat_manager.process_message(message)
                    print(f"\nAssistant: {response}")
            else:
                # Regular message processing
                response = self.chat_manager.process_message(message)
                print(f"\nAssistant: {response}")

            # Auto-save after each message
            self.chat_manager.save_conversation()
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            print(f"Error processing message: {e}")

    def print_welcome(self):
        """Print the welcome message."""
        print("\n" + "=" * 60)
        print("Welcome to the Chat Terminal")
        print("=" * 60)
        print("Type messages to chat with the assistant.")
        print("Commands start with '/' (e.g., '/help').")
        print("=" * 60)
        print("Storage directory:", self.storage_dir)
        print("User ID:", self.user_id)
        print("LLM provider:", self.llm_provider)
        if self.model_name:
            print("Model:", self.model_name)
        print("=" * 60)

        # Show progress feature availability
        if (isinstance(self.chat_manager.llm, (MCPSSELLM, MCPHTTPLLM)) and FASTMCP_AVAILABLE):
            print("📊 Progress display enabled for direct tool calls")
            print("   Try: 'long_task' to see incremental progress")
        elif isinstance(self.chat_manager.llm, (MCPSSELLM, MCPHTTPLLM)):
            print("⚠️  FastMCP not available - no progress display for tool calls")

        print("Type '/help' for available commands.")
        print("=" * 60)

    # Command handlers

    def cmd_help(self, *args):
        """Show help information."""
        print("\nAvailable commands:")
        for cmd, desc in self.command_help.items():
            print(f"  /{cmd:<20} - {desc}")
        print("\nTo chat, simply type your message and press Enter.")

    def cmd_new(self, *args):
        """Create a new conversation."""
        title = " ".join(args) if args else None
        if not title:
            # Generate a default title with timestamp
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            title = f"Conversation {timestamp}"

        # Create the conversation
        conversation = self.chat_manager.create_conversation(
            title=title,
            user_id=self.user_id
        )

        print(f"Created new conversation: {conversation.title}")
        print(f"ID: {conversation.conversation_id}")

        # Add a system message to start, but only if not using MCP
        # (MCP/Anthropic API handles system messages differently)
        if not (isinstance(self.chat_manager.llm, MCPSSELLM) or isinstance(self.chat_manager.llm, MCPHTTPLLM)):
            self.chat_manager.add_message("system", "How can I help you today?")

        # Save the new conversation
        self.chat_manager.save_conversation()

    def cmd_list(self, *args):
        """List available conversations."""
        year = args[0] if len(args) > 0 and args[0].isdigit() else None
        month = args[1] if len(args) > 1 and args[1].isdigit() else None

        # Get conversations
        conversations = self.chat_manager.list_conversations(
            user_id=self.user_id if self.user_id != "anonymous" else None,
            year=year,
            month=month
        )

        if not conversations:
            print("No conversations found.")
            return

        # Display conversations
        print(f"\nFound {len(conversations)} conversations:")
        for i, conv in enumerate(conversations, 1):
            # Format the date for display
            created_date = datetime.fromisoformat(conv['created_at']).strftime("%Y-%m-%d %H:%M:%S")

            # Display conversation info
            print(f"{i}. {conv['title']}")
            print(f"   ID: {conv['conversation_id']}")
            print(f"   Created: {created_date}")
            print(f"   Messages: {conv['message_count']}")
            print(f"   Path: {conv['relative_path']}")
            print()

    def cmd_load(self, *args):
        """Load a conversation by ID or path."""
        if not args:
            print("Please provide a conversation ID or path.")
            return

        conversation_id_or_path = args[0]

        # Try to load the conversation
        conversation = self.chat_manager.set_active_conversation(conversation_id_or_path)

        if conversation:
            print(f"Loaded conversation: {conversation.title}")
            print(f"ID: {conversation.conversation_id}")

            # Show the last few messages for context
            if conversation.messages:
                print("\nRecent messages:")
                # Get the last 5 messages or all if fewer
                recent_msgs = conversation.messages[-5:] if len(conversation.messages) > 5 else conversation.messages
                for msg in recent_msgs:
                    role = msg['role'].capitalize()
                    content = msg['content']
                    # Truncate long messages for display
                    if len(content) > 100:
                        content = content[:97] + "..."
                    print(f"{role}: {content}")
        else:
            print(f"Conversation not found: {conversation_id_or_path}")

    def cmd_save(self, *args):
        """Save the current conversation."""
        if not self.chat_manager.active_conversation:
            print("No active conversation to save.")
            return

        try:
            file_path = self.chat_manager.save_conversation()
            print(f"Conversation saved to {file_path}")
        except Exception as e:
            logger.error(f"Error saving conversation: {e}")
            print(f"Error saving conversation: {e}")

    def cmd_delete(self, *args):
        """Delete a conversation by ID or path."""
        if not args:
            print("Please provide a conversation ID or path.")
            return

        conversation_id_or_path = args[0]

        # Confirm deletion
        confirm = input(f"Are you sure you want to delete conversation '{conversation_id_or_path}'? (y/n): ")
        if confirm.lower() not in ['y', 'yes']:
            print("Deletion cancelled.")
            return

        # Try to delete the conversation
        success = self.chat_manager.delete_conversation(conversation_id_or_path)

        if success:
            print(f"Conversation deleted: {conversation_id_or_path}")
        else:
            print(f"Failed to delete conversation: {conversation_id_or_path}")

    def cmd_info(self, *args):
        """Show information about the current conversation."""
        if not self.chat_manager.active_conversation:
            print("No active conversation.")
            return

        conv = self.chat_manager.active_conversation

        # Format dates for display
        created_date = datetime.fromisoformat(conv.created_at).strftime("%Y-%m-%d %H:%M:%S")
        updated_date = datetime.fromisoformat(conv.updated_at).strftime("%Y-%m-%d %H:%M:%S")

        # Display conversation info
        print(f"\nConversation: {conv.title}")
        print(f"ID: {conv.conversation_id}")
        print(f"User ID: {conv.user_id or 'None'}")
        print(f"Created: {created_date}")
        print(f"Updated: {updated_date}")
        print(f"Messages: {len(conv.messages)}")

        # Display metadata if any
        if conv.metadata:
            print("\nMetadata:")
            for key, value in conv.metadata.items():
                print(f"  {key}: {value}")

        # Display message distribution
        roles = {}
        for msg in conv.messages:
            role = msg['role']
            roles[role] = roles.get(role, 0) + 1

        print("\nMessage distribution:")
        for role, count in roles.items():
            print(f"  {role}: {count}")

    def cmd_stats(self, *args):
        """Show statistics about stored conversations."""
        stats = self.chat_manager.get_conversation_stats()

        print("\nConversation Statistics:")
        print(f"Total conversations: {stats['total_conversations']}")

        print("\nBy Year/Month:")
        for year, year_data in sorted(stats["years"].items()):
            print(f"  {year}: {year_data['total']} conversations")
            for month, count in sorted(year_data["months"].items()):
                print(f"    {month}: {count} conversations")

        print("\nBy User:")
        for user_id, count in sorted(stats["users"].items()):
            print(f"  {user_id}: {count} conversations")

    def cmd_clear(self, *args):
        """Clear the terminal screen."""
        os.system('cls' if os.name == 'nt' else 'clear')
        self.print_welcome()

    def cmd_exit(self, *args):
        """Exit the chat terminal."""
        # Save the active conversation if any
        if self.chat_manager.active_conversation:
            try:
                self.chat_manager.save_conversation()
                print("Active conversation saved.")
            except Exception as e:
                logger.error(f"Error saving conversation on exit: {e}")
                print(f"Error saving conversation: {e}")

        print("Exiting chat terminal. Goodbye!")
        self.running = False

    def cmd_load_context(self, *args):
        """Load contextual data from a JSON file into the current conversation."""
        if not args:
            print("Please provide a context filename.")
            print("Example: /load_context companies_example.json")
            return

        filename = args[0]

        # Construct the full path to the context file
        if not filename.endswith('.json'):
            filename = f"{filename}.json"

        context_file_path = os.path.join(self.context_dir, filename)

        # Check if the file exists
        if not os.path.exists(context_file_path):
            print(f"Context file not found: {context_file_path}")
            print(f"Available context files in {self.context_dir}:")
            try:
                if os.path.exists(self.context_dir):
                    context_files = [f for f in os.listdir(self.context_dir) if f.endswith('.json')]
                    if context_files:
                        for f in sorted(context_files):
                            print(f"  - {f}")
                    else:
                        print("  No JSON files found in context directory.")
                else:
                    print("  Context directory does not exist.")
            except Exception as e:
                print(f"  Error listing context files: {e}")
            return

        try:
            # Load the JSON context file
            with open(context_file_path, 'r', encoding='utf-8') as f:
                context_data = json.load(f)

            # Ensure we have an active conversation
            if not self.chat_manager.active_conversation:
                # Create a new conversation with a title based on the context
                context_title = context_data.get('title', f"Context: {filename}")
                conversation = self.chat_manager.create_conversation(
                    title=context_title,
                    user_id=self.user_id
                )
                print(f"Created new conversation: {conversation.title}")

            # Convert the context data to a system message
            context_message = self._format_context_data(context_data, filename)

            # Add the context as a system message
            self.chat_manager.add_message("system", context_message)

            # Save the conversation with the new context
            self.chat_manager.save_conversation()

            print(f"Successfully loaded context from {filename}")
            print(f"Context type: {context_data.get('type', 'unknown')}")
            print(f"Description: {context_data.get('description', 'No description available')}")

            # Show summary of loaded data
            if 'data' in context_data and isinstance(context_data['data'], list):
                print(f"Loaded {len(context_data['data'])} items")

        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON context file {filename}: {e}")
            print(f"Error: Invalid JSON format in {filename}: {e}")
        except Exception as e:
            logger.error(f"Error loading context file {filename}: {e}")
            print(f"Error loading context file: {e}")

    def _format_context_data(self, context_data: Dict[str, Any], filename: str) -> str:
        """Format context data into a system message for the LLM.

        Args:
            context_data: The loaded JSON context data.
            filename: The name of the context file.

        Returns:
            A formatted string to be used as a system message.
        """
        # Start with basic information
        message_parts = [
            f"CONTEXT LOADED: {filename}",
            f"Type: {context_data.get('type', 'unknown')}",
            f"Title: {context_data.get('title', 'No title')}",
            f"Description: {context_data.get('description', 'No description')}"
        ]

        # Add the actual data
        if 'data' in context_data:
            data = context_data['data']
            if isinstance(data, list):
                message_parts.append(f"\nData ({len(data)} items):")
                for i, item in enumerate(data, 1):
                    if isinstance(item, dict):
                        # Format dictionary items nicely
                        item_str = f"  {i}. "
                        if 'name' in item:
                            item_str += f"Name: {item['name']}"
                        elif 'title' in item:
                            item_str += f"Title: {item['title']}"
                        elif 'id' in item:
                            item_str += f"ID: {item['id']}"

                        # Add other key fields
                        for key, value in item.items():
                            if key not in ['name', 'title', 'id']:
                                item_str += f", {key}: {value}"

                        message_parts.append(item_str)
                    else:
                        message_parts.append(f"  {i}. {item}")
            elif isinstance(data, dict):
                message_parts.append("\nData:")
                for key, value in data.items():
                    message_parts.append(f"  {key}: {value}")
            else:
                message_parts.append(f"\nData: {data}")

        message_parts.append("\nThis contextual information is now available for reference in our conversation.")

        return "\n".join(message_parts)


def parse_args():
    """Parse command line arguments.

    Returns:
        Parsed arguments namespace.
    """
    parser = argparse.ArgumentParser(description="Terminal-based chat interface")
    parser.add_argument(
        "--storage-dir",
        default="./conversations",
        help="Directory to store conversations (default: ./conversations)"
    )
    parser.add_argument(
        "--user-id",
        default=None,
        help="User ID to associate with conversations (default: system username)"
    )

    # Define available LLM providers
    llm_choices = ["mock", "openai", "anthropic"]
    if MCP_SSE_AVAILABLE:
        llm_choices.append("mcp")
    if MCP_HTTP_AVAILABLE:
        llm_choices.append("mcp-http")

    parser.add_argument(
        "--llm",
        default="mock",
        choices=llm_choices,
        help=f"LLM provider to use (default: mock, available: {', '.join(llm_choices)})"
    )
    parser.add_argument(
        "--model",
        default=None,
        help="Specific model name to use with the selected LLM provider. For MCP providers, this can be a server URL."
    )
    parser.add_argument(
        "--mcp-server",
        default="http://0.0.0.0:9000/sse",
        help="MCP SSE server URL when using the MCP provider (default: http://0.0.0.0:9000/sse)"
    )
    parser.add_argument(
        "--mcp-http-server",
        default="http://0.0.0.0:9000/mcp",
        help="MCP HTTP server URL when using the MCP-HTTP provider (default: http://0.0.0.0:9000/mcp)"
    )
    return parser.parse_args()


if __name__ == "__main__":
    # Parse command line arguments
    args = parse_args()

    # Set the model name based on the provider
    if args.llm == "mcp" and not args.model:
        # If using MCP SSE provider and no model is specified, use the MCP server URL as the model
        model_name = args.mcp_server
    elif args.llm == "mcp-http" and not args.model:
        # If using MCP HTTP provider and no model is specified, use the MCP HTTP server URL as the model
        model_name = args.mcp_http_server
    else:
        model_name = args.model

    # Create and start the chat terminal
    terminal = ChatTerminal(
        storage_dir=args.storage_dir,
        user_id=args.user_id,
        llm_provider=args.llm,
        model_name=model_name
    )
    terminal.start()
