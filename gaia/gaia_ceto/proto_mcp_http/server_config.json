{"description": "Enhanced MCP Server Configuration (Augment Pattern)", "globalSettings": {"defaultTimeout": 120, "longRunningToolTimeout": 300, "showToolOutput": false, "description": "Global settings. defaultTimeout/longRunningToolTimeout are in seconds for tool execution. showToolOutput controls whether tool call results are displayed in chat_term output (useful for debugging but not for production)."}, "mcpServers": {"firecrawl-mcp": {"enabled": true, "command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "{FIRECRAWL_API_KEY}"}, "namespace": "fc", "description": "Firecrawl MCP server spawned via npx", "parameterMapping": {"firecrawl_scrape": {}, "firecrawl_map": {}, "firecrawl_search": {"q": "query"}}, "defaultParameters": {"firecrawl_scrape": {"formats": ["markdown"], "onlyMainContent": true}, "firecrawl_search": {"limit": 2, "lang": "en", "country": "us"}}}, "firecrawl-hosted": {"enabled": false, "url": "https://mcp.firecrawl.dev/{FIRECRAWL_API_KEY}/sse", "protocol": "sse", "namespace": "web", "description": "Firecrawl hosted MCP server", "timeout": 600, "toolTimeouts": {"firecrawl_scrape": 600, "firecrawl_crawl": 600, "firecrawl_deep_research": 600, "firecrawl_map": 600, "firecrawl_search": 600}, "parameterMapping": {"firecrawl_scrape": {}, "firecrawl_map": {}, "firecrawl_search": {"q": "query"}}, "defaultParameters": {"firecrawl_scrape": {"formats": ["markdown"], "onlyMainContent": true}}}, "exa-mcp": {"enabled": true, "command": "npx", "args": ["-y", "exa-mcp"], "env": {"EXA_API_KEY": "{EXA_API_KEY}"}, "namespace": "exa", "description": "Embedding-based web search with Exa"}, "brave-search-mcp": {"enabled": true, "command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_SEARCH_API_KEY": "{BRAVE_SEARCH_API_KEY}"}, "namespace": "search", "description": "Brave Search API for web and local search", "parameterMapping": {"brave_web_search": {"q": "query"}, "brave_local_search": {"q": "query"}}, "defaultParameters": {"brave_web_search": {"count": 10}, "brave_local_search": {"count": 5}}}}, "chat_term_usage": {"description": "Connect chat_term to this enhanced server", "command": "python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://localhost:9000/mcp", "available_tools": ["echostring (local)", "echostring_table (local)", "long_task (local)", "server_status (local)", "list_all_tools (local)", "third_party_health (local)", "fc_firecrawl_scrape (delegated)", "fs_read_file (delegated)"]}}