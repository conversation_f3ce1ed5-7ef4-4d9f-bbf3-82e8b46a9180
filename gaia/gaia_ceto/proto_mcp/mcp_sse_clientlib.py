"""
MCP SSE Client Library

This module provides a reusable client library for interacting with MCP servers via SSE.
It handles the core functionality of connecting to an MCP server, calling tools,
and processing queries with <PERSON>.
"""

import asyncio
from typing import Optional, List, Dict, Any, Callable, Union
from contextlib import AsyncExitStack
import logging

# Import ClientSession and types from mcp core
from mcp import ClientSession, types
# Import sse_client specifically for SSE transport
from mcp.client.sse import sse_client

from anthropic import Anthropic

# Configure logging
logger = logging.getLogger(__name__)

# --- Configuration ---
DEFAULT_SERVER_URL = "http://0.0.0.0:8000/sse"  # Default URL for SSE
DEFAULT_MODEL = "claude-3-5-sonnet-20240620"  # Default Claude model

class ToolCallResult:
    """Represents the result of a tool call, with additional metadata."""

    def __init__(self,
                 tool_name: str,
                 tool_input: Any,
                 tool_call_id: str,
                 success: bool = True,
                 content: Optional[str] = None,
                 error: Optional[str] = None,
                 execution_time: Optional[float] = None):
        self.tool_name = tool_name
        self.tool_input = tool_input
        self.tool_call_id = tool_call_id
        self.success = success
        self.content = content
        self.error = error
        self.execution_time = execution_time
        self.timestamp = None  # Can be set by the caller if needed

    def to_dict(self) -> Dict[str, Any]:
        """Convert to a dictionary for storage or serialization."""
        return {
            "tool_name": self.tool_name,
            "tool_input": self.tool_input,
            "tool_call_id": self.tool_call_id,
            "success": self.success,
            "content": self.content,
            "error": self.error,
            "execution_time": self.execution_time,
            "timestamp": self.timestamp
        }

    def to_tool_result_block(self) -> Dict[str, Any]:
        """Convert to a tool result block for Claude API."""
        result = {
            "type": "tool_result",
            "tool_use_id": self.tool_call_id,
        }

        if not self.success:
            result["is_error"] = True
            result["content"] = self.error or f"Error executing tool {self.tool_name}."
        else:
            result["content"] = self.content or ""

        return result


class MCPClientLib:
    """Core client library for interacting with MCP servers via SSE."""

    def __init__(self,
                 anthropic_api_key: Optional[str] = None,
                 debug_callback: Optional[Callable] = None):
        """Initialize the MCP client library.

        Args:
            anthropic_api_key: Optional API key for Anthropic. If not provided,
                               it will be loaded from environment variables.
            debug_callback: Optional callback function for debug messages.
                            Function signature: callback(level: str, message: str, data: Any)
        """
        # Initialize session and client objects
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.available_tools = []
        self.connected = False
        self.debug_callback = debug_callback
        self.global_settings = {}  # Store global settings from server

        # Handle Anthropic API key
        try:
            import os
            # If API key is provided, use it
            if anthropic_api_key:
                self.anthropic = Anthropic(api_key=anthropic_api_key)
            # Otherwise, try to get it from environment variables
            elif "ANTHROPIC_API_KEY" in os.environ:
                self.anthropic = Anthropic(api_key=os.environ["ANTHROPIC_API_KEY"])
            else:
                # Try to create without explicit key (may use ~/.anthropic/config.json)
                self.anthropic = Anthropic()
                # Test if it works
                if not hasattr(self.anthropic, "api_key") or not self.anthropic.api_key:
                    # Use logger directly to avoid async issues during initialization
                    logger.warning("No Anthropic API key found. LLM functionality will not work.")
                    print("No Anthropic API key found. LLM functionality will not work.")
        except Exception as e:
            # Use logger directly to avoid async issues during initialization
            logger.error(f"Error initializing Anthropic client: {e}")
            print(f"Error initializing Anthropic client: {e}")
            self.anthropic = None

    def should_show_tool_output(self) -> bool:
        """Check if tool output should be shown based on global settings."""
        return self.global_settings.get('showToolOutput', True)  # Default to True for backward compatibility

    async def _debug(self, level: str, message: str, data: Any = None):
        """Send debug information to the callback if provided."""
        if self.debug_callback:
            try:
                # Call the debug callback and await it if it's a coroutine
                result = self.debug_callback(level, message, data)
                if asyncio.iscoroutine(result):
                    await result
            except Exception as e:
                # Don't let debug callback errors affect the main flow
                logger.error(f"Error in debug callback: {e}")

        # Also log using the standard logging module
        if level == "info":
            logger.info(message)
        elif level == "warning":
            logger.warning(message)
        elif level == "error":
            logger.error(message)
        elif level == "debug":
            logger.debug(message)

    async def connect_to_server(self, server_url: str = DEFAULT_SERVER_URL) -> bool:
        """Connect to an MCP server via SSE transport.

        Args:
            server_url: The HTTP URL of the server's SSE endpoint.

        Returns:
            bool: True if connection was successful, False otherwise.
        """
        await self._debug("info", f"Connecting to MCP server via SSE at: {server_url}")

        try:
            # Establish SSE transport connection using the provided URL
            sse_transport = await self.exit_stack.enter_async_context(sse_client(server_url))
            read_stream, write_stream = sse_transport
            await self._debug("debug", "SSE transport connection established successfully.")

            # Create and initialize the MCP ClientSession using the SSE streams
            self.session = await self.exit_stack.enter_async_context(ClientSession(read_stream, write_stream))
            await self._debug("info", "Initializing MCP session (performing handshake)...")
            await self.session.initialize()
            await self._debug("info", "MCP session initialized successfully.")

            # List available tools from the connected server
            response = await self.session.list_tools()
            tools = response.tools
            tool_names = [tool.name for tool in tools]
            await self._debug("info", f"Connected to server with tools: {', '.join(tool_names)}", tools)

            # Prepare tools in the format expected by Anthropic API
            self.available_tools = []
            for tool in tools:
                self.available_tools.append({
                    "name": tool.name,
                    "description": tool.description,
                    "input_schema": tool.inputSchema
                })

            self.connected = True

            # Try to fetch global settings from the server
            try:
                settings_result = await self.call_tool("get_global_settings", {})
                if settings_result.success and settings_result.content:
                    import json
                    self.global_settings = json.loads(settings_result.content)
                    await self._debug("info", f"Loaded global settings: {self.global_settings}")
                else:
                    await self._debug("warning", "Could not fetch global settings from server")
            except Exception as e:
                await self._debug("warning", f"Failed to fetch global settings: {e}")

            return True

        except ConnectionRefusedError as e:
            await self._debug("error", f"Connection Error: Could not connect to the server at {server_url}.", e)
            await self._debug("error", "Please ensure the MCP server process is running, accessible, and listening on the correct address and port with SSE enabled.")
            self.connected = False
            return False

        except Exception as e:
            await self._debug("error", f"An unexpected error occurred during connection or initialization: {e}", e)
            self.connected = False
            return False

    async def call_tool(self,
                        tool_name: str,
                        tool_input: Any,
                        tool_call_id: str = "manual_call",
                        timeout: Optional[float] = None) -> ToolCallResult:
        """Call a tool via the MCP session with optional timeout.

        Args:
            tool_name: Name of the tool to call
            tool_input: Input for the tool
            tool_call_id: ID for the tool call (for tracking)
            timeout: Optional timeout in seconds

        Returns:
            ToolCallResult: Object containing the result and metadata
        """
        if not self.session:
            return ToolCallResult(
                tool_name=tool_name,
                tool_input=tool_input,
                tool_call_id=tool_call_id,
                success=False,
                error="Not connected to MCP server"
            )

        # Ensure tool_input is properly formatted as a dictionary
        formatted_input = tool_input

        # Handle echostring tool specifically
        if tool_name == "echostring":
            # Case 1: If input is a string, convert to {"phrase": string}
            if isinstance(tool_input, str):
                formatted_input = {"phrase": tool_input}
                await self._debug("info", f"Converted string input to dictionary: {formatted_input}")
            # Case 2: If input is a dict without "phrase" key but with other keys
            elif isinstance(tool_input, dict) and "phrase" not in tool_input and len(tool_input) > 0:
                # Use the first value as the phrase
                first_key = next(iter(tool_input))
                formatted_input = {"phrase": tool_input[first_key]}
                await self._debug("info", f"Extracted phrase from dictionary: {formatted_input}")

        await self._debug("info", f"Calling tool: '{tool_name}' with input: {formatted_input}")

        import time
        start_time = time.time()

        try:
            # Check session status before calling tool
            if not self.session:
                return ToolCallResult(
                    tool_name=tool_name,
                    tool_input=tool_input,
                    tool_call_id=tool_call_id,
                    success=False,
                    error="No MCP session available",
                    execution_time=0.0
                )

            await self._debug("debug", f"Session status: {type(self.session)}, calling tool...")

            # Call the tool with timeout if specified
            if timeout:
                task = asyncio.create_task(self.session.call_tool(tool_name, formatted_input))
                try:
                    mcp_result = await asyncio.wait_for(task, timeout=timeout)
                except asyncio.TimeoutError:
                    task.cancel()
                    execution_time = time.time() - start_time
                    return ToolCallResult(
                        tool_name=tool_name,
                        tool_input=tool_input,  # Keep original input for reference
                        tool_call_id=tool_call_id,
                        success=False,
                        error=f"Tool call timed out after {timeout} seconds",
                        execution_time=execution_time
                    )
            else:
                mcp_result = await self.session.call_tool(tool_name, formatted_input)

            execution_time = time.time() - start_time

            # Process the result
            if mcp_result.isError:
                await self._debug("warning", f"Tool '{tool_name}' reported an error.", mcp_result)
                return ToolCallResult(
                    tool_name=tool_name,
                    tool_input=tool_input,
                    tool_call_id=tool_call_id,
                    success=False,
                    error=mcp_result.content if hasattr(mcp_result, 'content') else "Unknown error",
                    execution_time=execution_time
                )
            else:
                await self._debug("info", f"Tool '{tool_name}' executed successfully.", mcp_result)
                return ToolCallResult(
                    tool_name=tool_name,
                    tool_input=tool_input,
                    tool_call_id=tool_call_id,
                    success=True,
                    content=mcp_result.content if hasattr(mcp_result, 'content') else str(mcp_result),
                    execution_time=execution_time
                )

        except Exception as e:
            execution_time = time.time() - start_time

            # Enhanced error logging to debug empty exception messages
            error_details = {
                'exception_type': type(e).__name__,
                'exception_str': str(e),
                'exception_repr': repr(e),
                'exception_args': e.args,
                'has_message': bool(str(e)),
                'message_length': len(str(e))
            }

            await self._debug("error", f"Error calling tool '{tool_name}' via MCP: {e}", error_details)

            # Create a more informative error message
            if str(e):
                error_msg = str(e)
            elif e.args:
                error_msg = str(e.args[0]) if e.args[0] else f"Empty {type(e).__name__} exception"
            else:
                error_msg = f"Unknown {type(e).__name__} exception with no details"

            return ToolCallResult(
                tool_name=tool_name,
                tool_input=tool_input,
                tool_call_id=tool_call_id,
                success=False,
                error=f"Client-side error calling tool {tool_name}: {error_msg}",
                execution_time=execution_time
            )

    async def process_query(self,
                           query: str,
                           model: str = DEFAULT_MODEL,
                           max_tokens: int = 1024,
                           tool_timeout: Optional[float] = None,
                           conversation_history: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """Process a query using Claude and available tools via the MCP session.

        Args:
            query: The user query to process
            model: Claude model to use
            max_tokens: Maximum tokens for Claude response
            tool_timeout: Optional timeout for tool calls in seconds
            conversation_history: Optional conversation history to include

        Returns:
            Dict containing:
                - final_text: The final response text
                - messages: Updated conversation history
                - tool_results: List of tool call results
                - error: Error message if any
        """
        if not self.session:
            return {
                "final_text": "Error: Not connected to MCP server.",
                "messages": [],
                "tool_results": [],
                "error": "Not connected to MCP server"
            }

        if not self.anthropic:
            return {
                "final_text": "Error: Anthropic API client is not available. Please check your API key.",
                "messages": conversation_history.copy() if conversation_history else [],
                "tool_results": [],
                "error": "Anthropic API client is not available. Please check your API key."
            }

        # Initialize or use provided conversation history
        all_messages = conversation_history.copy() if conversation_history else []

        # Extract system messages and filter them out from the messages array
        system_messages = []
        messages = []
        for msg in all_messages:
            if msg.get("role") == "system":
                system_messages.append(msg["content"])
            else:
                messages.append(msg)

        # Combine system messages into a single system prompt
        system_prompt = "\n".join(system_messages) if system_messages else None

        # Add the user query if not already in history
        if not messages or messages[-1]["role"] != "user" or messages[-1]["content"] != query:
            messages.append({"role": "user", "content": query})

        final_response_parts = []  # Store parts of the final response text
        tool_results = []  # Store tool call results

        await self._debug("info", "Starting query processing", {"query": query, "messages": messages})

        try:
            # Initial call to Claude
            await self._debug("debug", "Calling Claude API with tools", self.available_tools)

            # Prepare API call parameters
            api_params = {
                "model": model,
                "max_tokens": max_tokens,
                "messages": messages,
                "tools": self.available_tools
            }

            # Add system prompt if available
            if system_prompt:
                api_params["system"] = system_prompt
                await self._debug("debug", f"Using system prompt: {system_prompt[:100]}...")

            response = self.anthropic.messages.create(**api_params)
            await self._debug("debug", "Received initial Claude response", response)

            # Append initial assistant message(s) to conversation history
            assistant_content_blocks = []
            for content_block in response.content:
                # Add text blocks immediately to final response
                if content_block.type == 'text':
                    final_response_parts.append(content_block.text)
                    assistant_content_blocks.append({"type": "text", "text": content_block.text})
                elif content_block.type == 'tool_use':
                    # Don't add tool use block to final text yet
                    assistant_content_blocks.append({
                        "type": "tool_use",
                        "id": content_block.id,
                        "name": content_block.name,
                        "input": content_block.input,
                    })

            if assistant_content_blocks:
                messages.append({"role": "assistant", "content": assistant_content_blocks})

            # Process tool calls if any were requested
            tool_use_blocks = [block for block in response.content if block.type == 'tool_use']

            if tool_use_blocks:
                await self._debug("info", f"Claude requested {len(tool_use_blocks)} tool call(s).")
                tool_results_content = []  # Content for the next user message to Claude

                for tool_use in tool_use_blocks:
                    tool_name = tool_use.name
                    tool_input = tool_use.input
                    tool_call_id = tool_use.id  # Get the ID for the result

                    await self._debug("info", f"Processing tool call: '{tool_name}'", tool_use)
                    # Add placeholder text to final response
                    final_response_parts.append(f"\n[Calling tool '{tool_name}'...]")

                    # Execute the tool call via MCP session
                    tool_result = await self.call_tool(
                        tool_name=tool_name,
                        tool_input=tool_input,
                        tool_call_id=tool_call_id,
                        timeout=tool_timeout
                    )

                    # Store the tool result for return
                    tool_results.append(tool_result)

                    # Convert to tool result block for Claude
                    tool_result_block = tool_result.to_tool_result_block()
                    tool_results_content.append(tool_result_block)

                    # Update final response based on tool result
                    if not tool_result.success:
                        final_response_parts.append(f"[Failed to call tool '{tool_name}'.]")
                    else:
                        final_response_parts.append(f"\n")

                await self._debug("debug", "Tool results content", tool_results_content)

                # Send tool results back to Claude
                messages.append({
                    "role": "user",
                    "content": tool_results_content
                })

                # Get Claude's response summarizing tool results
                await self._debug("debug", "Calling Claude API with tool results")

                # Prepare follow-up API call parameters
                follow_up_params = {
                    "model": model,
                    "max_tokens": max_tokens,
                    "messages": messages,
                    # No tools needed for this follow-up
                }

                # Add system prompt if available
                if system_prompt:
                    follow_up_params["system"] = system_prompt

                follow_up_response = self.anthropic.messages.create(**follow_up_params)
                await self._debug("debug", "Received follow-up Claude response", follow_up_response)

                # Add Claude's final text response
                for content_block in follow_up_response.content:
                    if content_block.type == 'text':
                        final_response_parts.append(content_block.text)

            # Join all collected response parts
            final_text = "\n".join(final_response_parts).strip()

            return {
                "final_text": final_text,
                "messages": messages,
                "tool_results": tool_results,
                "error": None
            }

        except Exception as e:
            await self._debug("error", f"Error during query processing: {e}", e)
            return {
                "final_text": f"An error occurred: {e}",
                "messages": messages,
                "tool_results": tool_results,
                "error": str(e)
            }

    async def cleanup(self):
        """Clean up resources by closing the AsyncExitStack with improved error handling."""
        await self._debug("info", "Cleaning up resources...")
        try:
            # Set connected to False first to prevent new operations
            self.connected = False

            # Give a small delay to let any pending operations complete
            await asyncio.sleep(0.05)

            # Close the exit stack with timeout
            await asyncio.wait_for(self.exit_stack.aclose(), timeout=2.0)
            await self._debug("info", "Cleanup complete.")
        except asyncio.TimeoutError:
            await self._debug("warning", "Cleanup timeout - some resources may not have been properly closed")
        except Exception as e:
            await self._debug("warning", f"Error during cleanup: {e} - some resources may not have been properly closed")


# Example usage in a command-line client
if __name__ == "__main__":
    import sys

    def debug_callback(level, message, data=None):
        """Simple debug callback that prints to console."""
        print(f"[{level.upper()}] {message}")
        if data and level == "debug":
            print(f"  Data: {data}")
        # Return None to indicate this is not a coroutine
        return None

    async def main():
        # Determine server URL: use command-line arg or default
        server_url = sys.argv[1] if len(sys.argv) > 1 else DEFAULT_SERVER_URL

        # Create client with debug callback
        client = MCPClientLib(debug_callback=debug_callback)

        try:
            # Connect to server
            success = await client.connect_to_server(server_url)
            if not success:
                print("Failed to connect to server. Exiting.")
                return

            # Simple chat loop
            print("\nMCP Client Library Test")
            print("Type your queries or 'quit' to exit.")

            while True:
                query = input("\nQuery: ").strip()

                if not query:
                    continue
                if query.lower() == 'quit':
                    print("Exiting chat loop.")
                    break

                # Process the query
                result = await client.process_query(query, tool_timeout=3600)  # for long-running tools

                if result["error"]:
                    print(f"\nError: {result['error']}")
                else:
                    print("\nClaude Response:")
                    print(result["final_text"])

                    # Print tool results if any
                    if result["tool_results"]:
                        print("\nTool Results:")
                        for i, tool_result in enumerate(result["tool_results"]):
                            print(f"  Tool {i+1}: {tool_result.tool_name}")
                            print(f"    Success: {tool_result.success}")
                            if tool_result.success:
                                print(f"    Content: {tool_result.content}")
                            else:
                                print(f"    Error: {tool_result.error}")

        except KeyboardInterrupt:
            print("\nExiting...")
        except Exception as e:
            print(f"\nFatal error: {e}")
        finally:
            # Ensure cleanup runs
            await client.cleanup()

    # Run the main function
    asyncio.run(main())
