#!/usr/bin/env python3
"""
Simple test to check if the showToolOutput flag is working.
"""

import subprocess
import sys
import os

# Test with chat_term
print("Testing showToolOutput flag with chat_term...")
print("=" * 50)

# Set environment variables
env = os.environ.copy()
env['PYTHONPATH'] = '/home/<USER>/django-projects/agbase_admin'

# Run chat_term with a simple command
cmd = [
    sys.executable, '-m', 'gaia.gaia_ceto.ceto_v002.chat_term',
    '--llm', 'mcp-http',
    '--mcp-http-server', 'http://localhost:9000/mcp'
]

print(f"Running command: {' '.join(cmd)}")
print("This will start chat_term. Type 'echostring hello' to test tool output.")
print("Look for debug messages about global settings.")
print("If showToolOutput=false, you should NOT see 'Tool Call 1:' output.")
print("Press Ctrl+C to exit when done testing.")

try:
    subprocess.run(cmd, env=env, cwd='/home/<USER>/django-projects/agbase_admin')
except KeyboardInterrupt:
    print("\nTest completed.")
